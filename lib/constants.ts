/**
 * Constants untuk AI Agent Integration
 */

// Default URLs
export const DEFAULT_AI_AGENT_URL = 'https://n8n.finityhub.ai/webhook-test/fmcg';

// Storage Keys
export const STORAGE_KEYS = {
  SESSION: 'fin_chat_session',
  USER: 'fin_chat_user',
  PREFERENCES: 'fin_chat_preferences',
} as const;

// Request Configuration
export const REQUEST_CONFIG = {
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

// Session Configuration
export const SESSION_CONFIG = {
  MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  CLEANUP_INTERVAL: 60 * 60 * 1000, // 1 hour in milliseconds
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Tidak dapat terhubung ke server. <PERSON>iksa koneksi internet Anda.',
  TIMEOUT_ERROR: 'Request timeout. Silakan coba lagi dalam beberapa saat.',
  VALIDATION_ERROR: 'Format data tidak valid. Silakan coba lagi.',
  SERVER_ERROR: 'Terjadi kesalahan pada server. Silakan coba lagi.',
  UNKNOWN_ERROR: 'Terjadi kesalahan yang tidak diketahui. Silakan coba lagi.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  SESSION_RESET: 'Session berhasil direset.',
  MESSAGE_SENT: 'Pesan berhasil dikirim.',
  CONNECTION_SUCCESS: 'Koneksi ke AI Agent berhasil.',
} as const;

// API Headers
export const API_HEADERS = {
  'Content-Type': 'application/json',
  'User-Agent': 'FinChat/1.0',
  'Accept': 'text/plain, application/json',
} as const;

// Development Configuration
export const DEV_CONFIG = {
  ENABLE_LOGGING: process.env.NODE_ENV === 'development',
  ENABLE_DEBUG: process.env.NODE_ENV === 'development',
  LOG_REQUESTS: process.env.NODE_ENV === 'development',
  LOG_RESPONSES: process.env.NODE_ENV === 'development',
} as const;
