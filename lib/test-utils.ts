import { AIAgentClient } from './api-client';
import { AIAgentRequest } from './schemas';

/**
 * Utility functions untuk testing AI Agent integration
 */

export class AIAgentTester {
  private client: AIAgentClient;

  constructor(baseUrl?: string) {
    this.client = new AIAgentClient(baseUrl);
  }

  /**
   * Test basic connectivity ke AI Agent
   */
  async testConnection(): Promise<{ success: boolean; message: string; duration?: number }> {
    const startTime = Date.now();

    try {
      const isConnected = await this.client.testConnection();
      const duration = Date.now() - startTime;

      return {
        success: isConnected,
        message: isConnected ? 'Connection successful' : 'Connection failed',
        duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      };
    }
  }

  /**
   * Test dengan sample payload
   */
  async testSampleRequest(): Promise<{ success: boolean; message: string; response?: string }> {
    const samplePayload: AIAgentRequest = {
      userId: 'test_user_001',
      sessionId: 'test_session_001',
      message: 'berikan list semua product category'
    };

    try {
      const response = await this.client.sendMessage(samplePayload);
      const responseText = await response.text();

      return {
        success: response.ok,
        message: `Status: ${response.status} ${response.statusText}`,
        response: responseText
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test multiple requests untuk load testing
   */
  async testMultipleRequests(count: number = 5): Promise<{
    success: boolean;
    results: Array<{ success: boolean; duration: number; error?: string }>;
    averageDuration: number;
    successRate: number;
  }> {
    const results: Array<{ success: boolean; duration: number; error?: string }> = [];

    for (let i = 0; i < count; i++) {
      const startTime = Date.now();

      try {
        const payload: AIAgentRequest = {
          userId: `test_user_${i}`,
          sessionId: `test_session_${i}`,
          message: `Test message ${i + 1}`
        };

        const response = await this.client.sendMessage(payload);
        const duration = Date.now() - startTime;

        results.push({
          success: response.ok,
          duration,
          error: response.ok ? undefined : `${response.status} ${response.statusText}`
        });
      } catch (error) {
        const duration = Date.now() - startTime;
        results.push({
          success: false,
          duration,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const averageDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    const successRate = (successCount / count) * 100;

    return {
      success: successCount > 0,
      results,
      averageDuration,
      successRate
    };
  }

  /**
   * Generate test report
   */
  async generateTestReport(): Promise<string> {
    const report: string[] = [];
    report.push('=== AI Agent Integration Test Report ===\n');

    // Test 1: Basic Connection
    report.push('1. Connection Test:');
    const connectionTest = await this.testConnection();
    report.push(`   Status: ${connectionTest.success ? '✅ PASS' : '❌ FAIL'}`);
    report.push(`   Message: ${connectionTest.message}`);
    report.push(`   Duration: ${connectionTest.duration}ms\n`);

    // Test 2: Sample Request
    report.push('2. Sample Request Test:');
    const sampleTest = await this.testSampleRequest();
    report.push(`   Status: ${sampleTest.success ? '✅ PASS' : '❌ FAIL'}`);
    report.push(`   Message: ${sampleTest.message}`);
    if (sampleTest.response) {
      report.push(`   Response: ${sampleTest.response.substring(0, 100)}...`);
    }
    report.push('');

    // Test 3: Load Test
    report.push('3. Load Test (5 requests):');
    const loadTest = await this.testMultipleRequests(5);
    report.push(`   Status: ${loadTest.success ? '✅ PASS' : '❌ FAIL'}`);
    report.push(`   Success Rate: ${loadTest.successRate.toFixed(1)}%`);
    report.push(`   Average Duration: ${loadTest.averageDuration.toFixed(0)}ms`);
    report.push('');

    report.push('=== End of Report ===');

    return report.join('\n');
  }
}

/**
 * Quick test function untuk development
 */
export async function quickTest(): Promise<void> {
  console.log('🧪 Starting AI Agent Quick Test...\n');

  const tester = new AIAgentTester();
  const report = await tester.generateTestReport();

  console.log(report);
}

/**
 * Test function yang bisa dipanggil dari browser console
 */
if (typeof window !== 'undefined') {
  (window as unknown as { testAIAgent: () => Promise<void> }).testAIAgent = quickTest;
}
