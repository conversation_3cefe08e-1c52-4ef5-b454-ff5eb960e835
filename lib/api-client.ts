import { AIAgentRequest, AIAgentRequestSchema } from './schemas';

export class AIAgentClient {
  private baseUrl: string;
  private timeout: number;

  constructor(baseUrl?: string, timeout: number = 30000) {
    this.baseUrl = baseUrl || process.env.AI_AGENT_URL || 'https://n8n.finityhub.ai/webhook-test/fmcg';
    this.timeout = timeout;
  }

  async sendMessage(payload: AIAgentRequest): Promise<Response> {
    // Validasi payload menggunakan Zod
    const validatedPayload = AIAgentRequestSchema.parse(payload);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'FinChat/1.0',
          'Accept': 'text/plain, application/json',
        },
        body: JSON.stringify(validatedPayload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`AI Agent responded with status: ${response.status} ${response.statusText}`);
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timeout after ${this.timeout}ms`);
      }

      throw error;
    }
  }

  // Method untuk test koneksi
  async testConnection(): Promise<boolean> {
    try {
      const testPayload: AIAgentRequest = {
        userId: 'test_user',
        sessionId: 'test_session',
        message: 'test connection'
      };

      const response = await this.sendMessage(testPayload);
      return response.ok;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  // Method untuk logging request/response
  static logRequest(payload: AIAgentRequest, url: string) {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 AI Agent Request:', {
        url,
        payload,
        timestamp: new Date().toISOString(),
      });
    }
  }

  static logResponse(response: Response, duration: number) {
    if (process.env.NODE_ENV === 'development') {
      console.log('📥 AI Agent Response:', {
        status: response.status,
        statusText: response.statusText,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
      });
    }
  }

  static logError(error: Error, payload?: AIAgentRequest) {
    console.error('❌ AI Agent Error:', {
      error: error.message,
      payload,
      timestamp: new Date().toISOString(),
    });
  }
}
