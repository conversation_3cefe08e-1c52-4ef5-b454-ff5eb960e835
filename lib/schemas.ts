import { z } from 'zod';

// Schema untuk request payload ke AI agent
export const AIAgentRequestSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  sessionId: z.string().min(1, "Session ID is required"),
  message: z.string().min(1, "Message is required"),
});

// Schema untuk response dari AI agent
export const AIAgentResponseSchema = z.object({
  response: z.string(),
  status: z.enum(['success', 'error']).optional(),
  error: z.string().optional(),
});

// Schema untuk chat message
export const ChatMessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string(),
  id: z.string().optional(),
  timestamp: z.date().optional(),
});

// Types yang diekspor dari schema
export type AIAgentRequest = z.infer<typeof AIAgentRequestSchema>;
export type AIAgentResponse = z.infer<typeof AIAgentResponseSchema>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;

// Utility function untuk generate session ID
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Utility function untuk generate user ID (bisa disesuaikan dengan auth system)
export function generateUserId(): string {
  return `user_${Math.random().toString(36).substr(2, 9)}`;
}
