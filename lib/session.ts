'use client';

import { generateSessionId, generateUserId } from './schemas';

// Key untuk localStorage
const SESSION_STORAGE_KEY = 'fin_chat_session';
const USER_STORAGE_KEY = 'fin_chat_user';

export interface SessionData {
  sessionId: string;
  userId: string;
  createdAt: string;
}

// Get atau create session
export function getOrCreateSession(): SessionData {
  if (typeof window === 'undefined') {
    // Server-side fallback
    return {
      sessionId: generateSessionId(),
      userId: generateUserId(),
      createdAt: new Date().toISOString(),
    };
  }

  try {
    // Cek apakah sudah ada session
    const existingSession = localStorage.getItem(SESSION_STORAGE_KEY);
    const existingUser = localStorage.getItem(USER_STORAGE_KEY);

    if (existingSession && existingUser) {
      const sessionData = JSON.parse(existingSession);
      const userData = JSON.parse(existingUser);
      
      return {
        sessionId: sessionData.sessionId,
        userId: userData.userId,
        createdAt: sessionData.createdAt,
      };
    }

    // Buat session baru
    const newSession: SessionData = {
      sessionId: generateSessionId(),
      userId: generateUserId(),
      createdAt: new Date().toISOString(),
    };

    // Simpan ke localStorage
    localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify({
      sessionId: newSession.sessionId,
      createdAt: newSession.createdAt,
    }));
    
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify({
      userId: newSession.userId,
    }));

    return newSession;
  } catch (error) {
    console.error('Error managing session:', error);
    // Fallback jika localStorage error
    return {
      sessionId: generateSessionId(),
      userId: generateUserId(),
      createdAt: new Date().toISOString(),
    };
  }
}

// Reset session (untuk logout atau reset)
export function resetSession(): SessionData {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(SESSION_STORAGE_KEY);
    localStorage.removeItem(USER_STORAGE_KEY);
  }
  
  return getOrCreateSession();
}

// Get current session tanpa create
export function getCurrentSession(): SessionData | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const existingSession = localStorage.getItem(SESSION_STORAGE_KEY);
    const existingUser = localStorage.getItem(USER_STORAGE_KEY);

    if (existingSession && existingUser) {
      const sessionData = JSON.parse(existingSession);
      const userData = JSON.parse(existingUser);
      
      return {
        sessionId: sessionData.sessionId,
        userId: userData.userId,
        createdAt: sessionData.createdAt,
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting current session:', error);
    return null;
  }
}
