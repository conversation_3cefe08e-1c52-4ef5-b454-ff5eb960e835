import { NextRequest } from "next/server";
import { AIAgentRequestSchema } from "@/lib/schemas";
import { AIAgentClient } from "@/lib/api-client";
import { z } from "zod";

// Schema untuk request dari client
const ClientRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string(),
  })),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
});

export async function POST(req: NextRequest) {
  let body: unknown;
  let messages: Array<{ role: 'user' | 'assistant', content: string }> = [];
  let userId: string | undefined;
  let sessionId: string | undefined;

  try {
    body = await req.json();

    // Validasi request dari client
    const validatedRequest = ClientRequestSchema.parse(body);
    ({ messages, userId, sessionId } = validatedRequest);

    // Ambil pesan terakhir dari user
    const lastUserMessage = messages
      .filter(m => m.role === 'user')
      .pop();

    if (!lastUserMessage) {
      return new Response(
        JSON.stringify({ error: "No user message found" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Buat payload untuk AI agent sesuai format yang diminta
    const aiAgentPayload = AIAgentRequestSchema.parse({
      userId: userId || "guest_user",
      sessionId: sessionId || `session_${Date.now()}`,
      message: lastUserMessage.content,
    });

    // Log request untuk debugging
    AIAgentClient.logRequest(aiAgentPayload, process.env.AI_AGENT_URL || "https://n8n.finityhub.ai/webhook-test/fmcg");

    // Buat instance AI agent client
    const aiClient = new AIAgentClient();
    const startTime = Date.now();

    // Kirim request ke AI agent
    const response = await aiClient.sendMessage(aiAgentPayload);
    const duration = Date.now() - startTime;

    // Log response untuk debugging
    AIAgentClient.logResponse(response, duration);

    // Return response sebagai stream untuk compatibility dengan useChat
    return new Response(response.body, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
      },
    });

  } catch (error) {
    // Log error untuk debugging
    AIAgentClient.logError(
      error instanceof Error ? error : new Error("Unknown error"),
      userId && sessionId && messages ? {
        userId: userId,
        sessionId: sessionId,
        message: messages.filter(m => m.role === 'user').pop()?.content || ""
      } : undefined
    );

    if (error instanceof z.ZodError) {
      return new Response(
        JSON.stringify({
          error: "Invalid request format",
          details: error.errors
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Return error response yang user-friendly
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    const isTimeout = errorMessage.includes("timeout");
    const isConnectionError = errorMessage.includes("fetch") || errorMessage.includes("network");

    let userFriendlyMessage = "Maaf, terjadi kesalahan. Silakan coba lagi.";
    if (isTimeout) {
      userFriendlyMessage = "Request timeout. Silakan coba lagi dalam beberapa saat.";
    } else if (isConnectionError) {
      userFriendlyMessage = "Tidak dapat terhubung ke server. Periksa koneksi internet Anda.";
    }

    return new Response(
      userFriendlyMessage,
      {
        status: 500,
        headers: { "Content-Type": "text/plain; charset=utf-8" }
      }
    );
  }
}
