'use client';

import { useState, useEffect } from 'react';
import { User, Clock, RefreshCw, Info } from 'lucide-react';
import { getCurrentSession, resetSession, type SessionData } from '@/lib/session';

interface SessionInfoProps {
  className?: string;
}

export default function SessionInfo({ className = '' }: SessionInfoProps) {
  const [session, setSession] = useState<SessionData | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const sessionData = getCurrentSession();
    setSession(sessionData);
  }, []);

  const handleResetSession = () => {
    const newSession = resetSession();
    setSession(newSession);
    // Refresh halaman untuk memulai session baru
    window.location.reload();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!session) {
    return null;
  }

  return (
    <div className={`bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 flex items-center justify-between text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors rounded-lg"
      >
        <div className="flex items-center gap-2">
          <Info size={16} className="text-gray-500 dark:text-gray-400" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Session Info
          </span>
        </div>
        <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
          <svg width="12" height="12" viewBox="0 0 12 12" className="text-gray-400">
            <path
              d="M2 4l4 4 4-4"
              stroke="currentColor"
              strokeWidth="1.5"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </button>

      {isExpanded && (
        <div className="px-3 pb-3 space-y-3 border-t border-gray-200 dark:border-gray-700 pt-3">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs">
              <User size={12} className="text-gray-400" />
              <span className="text-gray-600 dark:text-gray-400">User ID:</span>
              <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-gray-800 dark:text-gray-200">
                {session.userId}
              </code>
            </div>

            <div className="flex items-center gap-2 text-xs">
              <RefreshCw size={12} className="text-gray-400" />
              <span className="text-gray-600 dark:text-gray-400">Session ID:</span>
              <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-gray-800 dark:text-gray-200 break-all">
                {session.sessionId}
              </code>
            </div>

            <div className="flex items-center gap-2 text-xs">
              <Clock size={12} className="text-gray-400" />
              <span className="text-gray-600 dark:text-gray-400">Created:</span>
              <span className="text-gray-800 dark:text-gray-200">
                {formatDate(session.createdAt)}
              </span>
            </div>
          </div>

          <button
            onClick={handleResetSession}
            className="w-full px-3 py-1.5 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800 rounded hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          >
            Reset Session
          </button>
        </div>
      )}
    </div>
  );
}
