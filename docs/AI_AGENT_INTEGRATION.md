# AI Agent Integration Documentation

## Overview

Chatbot ini telah diintegrasikan dengan AI Agent yang mengirimkan request payload dalam format yang telah ditentukan. Implementasi menggunakan Zod untuk schema validation dan session management untuk tracking user interactions.

## Request Payload Format

Chatbot mengirimkan payload ke AI Agent dengan format berikut:

```json
{
  "userId": "user001",
  "sessionId": "session_0001", 
  "message": "berikan list semua product category"
}
```

## URL Endpoint

Default URL: `https://n8n.finityhub.ai/webhook-test/fmcg`

URL dapat dikonfigurasi melalui environment variable `AI_AGENT_URL`.

## Schema Validation

Menggunakan Zod untuk validasi:

```typescript
const AIAgentRequestSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  sessionId: z.string().min(1, "Session ID is required"), 
  message: z.string().min(1, "Message is required"),
});
```

## Session Management

- **User ID**: Dihasilkan otomatis dan disimpan di localStorage
- **Session ID**: Dihasilkan untuk setiap session dan dapat direset
- **Persistence**: Session data disimpan di browser localStorage

## File Structure

```
lib/
├── schemas.ts          # Zod schemas dan types
├── session.ts          # Session management utilities
└── api-client.ts       # AI Agent client dengan logging

components/
├── Chat.tsx            # Main chat component (modified)
├── SessionInfo.tsx     # Session information display
└── Sidebar.tsx         # Sidebar with session info (modified)

app/api/chat/
└── route.ts            # API route handler (modified)
```

## Environment Variables

Buat file `.env.local` dengan:

```env
AI_AGENT_URL=https://n8n.finityhub.ai/webhook-test/fmcg
```

## Features

### 1. Automatic Session Management
- User ID dan Session ID dihasilkan otomatis
- Disimpan di localStorage untuk persistence
- Dapat direset melalui UI

### 2. Schema Validation
- Request dan response divalidasi menggunakan Zod
- Error handling yang comprehensive
- Type safety di seluruh aplikasi

### 3. Logging & Debugging
- Request/response logging di development mode
- Error tracking dengan context
- Performance monitoring

### 4. Error Handling
- User-friendly error messages
- Timeout handling
- Connection error detection
- Graceful fallbacks

## Usage Examples

### Basic Chat
User mengirim pesan "berikan list semua product category", sistem akan:

1. Mengambil session data dari localStorage
2. Membuat payload dengan format yang benar
3. Mengirim ke AI Agent endpoint
4. Menampilkan response ke user

### Session Reset
User dapat mereset session melalui SessionInfo component di sidebar.

## API Flow

```
User Input → Chat Component → API Route → AI Agent Client → N8N Webhook
                ↓
Response ← Chat Component ← API Route ← AI Agent Response
```

## Testing

Untuk testing koneksi AI Agent:

```typescript
import { AIAgentClient } from '@/lib/api-client';

const client = new AIAgentClient();
const isConnected = await client.testConnection();
console.log('AI Agent connected:', isConnected);
```

## Troubleshooting

### Common Issues

1. **Session tidak tersimpan**: Pastikan localStorage tersedia di browser
2. **Request timeout**: Periksa koneksi internet dan endpoint URL
3. **Validation error**: Pastikan payload sesuai dengan schema

### Debug Mode

Set `NODE_ENV=development` untuk melihat detailed logging di console.
